<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Touch Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }
        
        .touch-area {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #4B0082, #FF2D55);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            touch-action: pan-x;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .touch-content {
            display: flex;
            width: 200%;
            gap: 20px;
            padding: 20px;
        }
        
        .touch-item {
            flex: 0 0 150px;
            height: 100px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .instructions {
            background: rgba(75, 0, 130, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Mobile Touch Test</h1>
        <p>Test the touch functionality on your mobile device</p>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ul>
                <li>Try swiping left and right on the colored area below</li>
                <li>The items should scroll horizontally</li>
                <li>Check if the scrolling feels smooth and responsive</li>
                <li>The status will update when you interact with the area</li>
            </ul>
        </div>
        
        <div class="touch-area" id="touchArea">
            <div class="touch-content">
                <div class="touch-item">Item 1</div>
                <div class="touch-item">Item 2</div>
                <div class="touch-item">Item 3</div>
                <div class="touch-item">Item 4</div>
                <div class="touch-item">Item 5</div>
                <div class="touch-item">Item 6</div>
                <div class="touch-item">Item 7</div>
                <div class="touch-item">Item 8</div>
            </div>
        </div>
        
        <div class="status" id="status">
            Status: Ready for testing
        </div>
        
        <div class="status">
            <strong>Device Info:</strong><br>
            Screen Width: <span id="screenWidth"></span>px<br>
            Touch Support: <span id="touchSupport"></span><br>
            User Agent: <span id="userAgent"></span>
        </div>
        
        <a href="index.html" style="color: #FF2D55; text-decoration: none; font-weight: bold;">← Back to Portfolio</a>
    </div>

    <script>
        const touchArea = document.getElementById('touchArea');
        const status = document.getElementById('status');
        const screenWidth = document.getElementById('screenWidth');
        const touchSupport = document.getElementById('touchSupport');
        const userAgent = document.getElementById('userAgent');
        
        // Display device info
        screenWidth.textContent = window.innerWidth;
        touchSupport.textContent = 'ontouchstart' in window ? 'Yes' : 'No';
        userAgent.textContent = navigator.userAgent.substring(0, 50) + '...';
        
        let touchStartTime = 0;
        let touchCount = 0;
        
        // Touch event listeners
        touchArea.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchCount++;
            status.textContent = `Touch started (${touchCount} touches total)`;
        });
        
        touchArea.addEventListener('touchmove', (e) => {
            status.textContent = `Touch moving... (${touchCount} touches total)`;
        });
        
        touchArea.addEventListener('touchend', (e) => {
            const duration = Date.now() - touchStartTime;
            status.textContent = `Touch ended after ${duration}ms (${touchCount} touches total)`;
        });
        
        // Scroll event listener
        touchArea.addEventListener('scroll', () => {
            status.textContent = `Scrolling... Position: ${Math.round(touchArea.scrollLeft)}px`;
        });
        
        // Mouse events for desktop testing
        touchArea.addEventListener('mousedown', () => {
            status.textContent = 'Mouse down (desktop mode)';
        });
        
        touchArea.addEventListener('mousemove', (e) => {
            if (e.buttons === 1) {
                status.textContent = 'Mouse dragging (desktop mode)';
            }
        });
        
        touchArea.addEventListener('mouseup', () => {
            status.textContent = 'Mouse up (desktop mode)';
        });
    </script>
</body>
</html>
